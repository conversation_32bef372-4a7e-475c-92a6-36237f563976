import React, { useState, useEffect } from 'react';
import { Form, Row, Col, Input, Button, Select, Radio } from 'antd';
import { connect } from 'dva';
import GetUser from '@/components/GetUser';
const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const { Option } = Select;

const STORAGE_KEY = 'crowdCircle_searchParams';

const SearchForm = props => {
  const {
    dispatch,
    crowdCircle: { searchParams, tabKey },
  } = props;
  const [form] = Form.useForm();

  // 保存筛选条件到 localStorage
  const saveSearchParams = (params) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(params));
    } catch (error) {
      console.error('保存筛选条件失败:', error);
    }
  };

  // 从 localStorage 恢复筛选条件
  const loadSearchParams = () => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      return saved ? JSON.parse(saved) : {};
    } catch (error) {
      console.error('加载筛选条件失败:', error);
      return {};
    }
  };

  // 组件初始化时恢复筛选条件
  useEffect(() => {
    // 检查是否是页面刷新
    const isPageRefresh = () => {
      if (window.performance && window.performance.navigation) {
        return window.performance.navigation.type === 1;
      }
      return false;
    };

    // 如果是页面刷新，不恢复筛选条件
    if (isPageRefresh()) {
      return;
    }

    const savedParams = loadSearchParams();
    if (Object.keys(savedParams).length > 0) {
      // 过滤掉 tabKey、分页相关的参数和分组相关参数，只恢复表单字段
      const { tabKey, pageNo, pageSize, groupId, categoryId, ...formParams } = savedParams;
      form.setFieldsValue(formParams);
      dispatch({
        type: 'crowdCircle/updateState',
        payload: {
          searchParams: formParams,
        },
      });

      // 如果有分组信息，同时恢复分组选中状态
      if (categoryId) {
        dispatch({
          type: 'crowdGroup/updateState',
          payload: {
            categoryId: categoryId,
          },
        });
      }
    }
  }, []);

  const onSubmit = () => {
    form.submit();
  };

  const onReset = () => {
    form.resetFields();
    // 清除保存的筛选条件
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('清除筛选条件失败:', error);
    }
    dispatch({
      type: 'crowdCircle/pageQuery',
      payload: {
        pageNo: 1,
        pageSize: 10,
        tabKey: 'MY_CROWDS',
      },
    });
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        tabKey: 'MY_CROWDS',
      },
    });
    // 重置分组选中状态
    dispatch({
      type: 'crowdGroup/updateState',
      payload: {
        categoryId: '-1',
      },
    });
  };

    const onFinish = values => {
    // 搜索时重置分组选中状态（搜索应该是全局搜索）
    dispatch({
      type: 'crowdGroup/updateState',
      payload: {
        categoryId: '-1',
      },
    });

    // 保存筛选条件到 localStorage，包含 tabKey，但重置 pageNo 为 1（新搜索从第一页开始）
    // 搜索时不保存分组信息，因为搜索是全局的
    const paramsToSave = { ...values, tabKey, pageNo: 1, categoryId: '-1' };
    saveSearchParams(paramsToSave);

    const data = {
      ...searchParams,
      ...values,
      tabKey,
      pageNo: 1,
    }
    if (data.searchEmpId) {
      data.searchEmpId = data.searchEmpId.empId;
    }
    // 确保搜索时不带分组过滤
    delete data.groupId;

    dispatch({
      type: 'crowdCircle/pageQuery',
      payload: data,
    });
  };

  return (
    <Form
            onValuesChange={(value, allValue) => {
        // 表单值变化时，保持当前的分组选中状态
        // 从 localStorage 获取当前保存的分组信息
        const currentSaved = loadSearchParams();
        const currentCategoryId = currentSaved.categoryId || '-1';

        // 保存表单值变化到 localStorage，包含 tabKey 和当前分组状态
        const paramsToSave = { ...allValue, tabKey, categoryId: currentCategoryId };
        if (currentSaved.groupId) {
          paramsToSave.groupId = currentSaved.groupId;
        }
        saveSearchParams(paramsToSave);

        dispatch({
          type: 'crowdCircle/updateState',
          payload: {
            searchParams: allValue,
          },
        });
      }}
      form={form}
      {...layout}
      initialValues={{
        validityType: "ALL"
      }}
      onFinish={onFinish}
    >
      <Row gutter={24}>
        <Col span={8}>
          <Form.Item name="content" label="人群信息">
            <Input
              allowClear
              placeholder="请输入人群id,人群名称"
              style={{ minWidth: 200 }}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item name="profileCodeList" label="人群类型">
            <Select
              options={[
                {
                  label: '淘宝id',
                  value: 'USER',
                },
                {
                  label: '设备id',
                  value: 'DEVICE',
                },
              ]}
              placeholder="请选择人群类型"
              mode="multiple"
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item name="circleTypeList" label="圈选方式">
            <Select mode="multiple" placeholder="请选择圈选方式" style={{ minWidth: 200 }}>
              <Option value="LABEL_CROWD">标签圈人</Option>
              <Option value="IMPORT_CROWD">人群导入</Option>
              <Option value="OPERATE_CROWD">人群组合</Option>
              <Option value="DYNAMIC_CROWD">动态人群</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24} style={{ marginBottom: 10 }}>
        <Col span={8}>
          <Form.Item name="timeTypeList" label="人群时效">
            <Select
              allowClear
              mode="multiple"
              options={[
                {
                  label: '实时人群',
                  value: 'ONLINE',
                },
                {
                  label: '离线人群',
                  value: 'OFFLINE',
                },
              ]}
              placeholder="请选择人群时效"
              style={{ minWidth: 200 }}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item name="crowdStatusList" label="人群状态">
            <Select
              allowClear
              mode="multiple"
              options={[
                {
                  label: '初始化',
                  value: 'INIT',
                },
                {
                  label: '创建中',
                  value: 'RUNNING',
                },
                {
                  label: '创建成功',
                  value: 'SUCCESS',
                },
                {
                  label: '创建失败',
                  value: 'ERROR',
                },
              ]}
              placeholder="请选择人群状态"
              style={{ minWidth: 200 }}
            ></Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item name="validityType" label="有效期" defaultValue="ALL">
            <Radio.Group >
              <Radio value="ALL">全部</Radio>
              <Radio value="EXPIRE_IN_7DAYS">7天内过期</Radio>
              <Radio value="VALID">未过期</Radio>
              <Radio value="EXPIRED">已过期</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item name="searchEmpId" label="负责人">
            <GetUser placeholder="请输入负责人花名或工号" />
          </Form.Item>
        </Col>
        <Col span={8} offset={8} style={{ textAlign: 'right' }}>
          <Button type="primary" onClick={onSubmit} style={{ marginRight: 10 }}>
            查询
          </Button>
          <Button htmlType="button" onClick={onReset}>
            重置
          </Button>
        </Col>
      </Row>
    </Form>
  );
};

export default connect(({ crowdCircle }) => ({
  crowdCircle,
}))(SearchForm);
