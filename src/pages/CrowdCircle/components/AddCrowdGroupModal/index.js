import { Modal, Form, Input } from 'antd';

const AddCrowdGroupModal = props => {
  const {
    handleFinish,
    form,
    modelConfig,
    onCancel,
  } = props;

  return (
    <Modal
      title={modelConfig.isEdit ? '编辑分组' : '新建分组'}
      visible={modelConfig.visible}
      onCancel={() => {
        onCancel();
        form.resetFields();
      }}
      onOk={() => form.submit()}
    >
      <Form form={form} onFinish={handleFinish} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
        <Form.Item
          label="分组名称"
          name="name"
          rules={[{ required: true, message: '请输入分组名称' }]}
        >
          <Input maxLength={20} placeholder="请输入分组名称,不能超过20个字符" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddCrowdGroupModal;
